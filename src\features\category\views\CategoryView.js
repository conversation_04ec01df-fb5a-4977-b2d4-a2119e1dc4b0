class CategoryView {
  constructor() {
    this.container = null;
  }

  render(categoryData) {
    // Clear the app container and replace its content
    const appContainer = document.getElementById('app');
    if (appContainer) {
      appContainer.innerHTML = '';
    }

    this.container = document.createElement('div');
    this.container.id = 'category-container';
    this.container.className = 'category-container';

    this.container.innerHTML = `
      <!-- Navigasi -->
      <nav class="category-nav">
        <div class="nav-container">
          <div class="nav-logo">
            <span class="nav-logo-main">Speaking Platform</span>
          </div>
          <div class="nav-buttons">
            <a href="#dashboard" class="nav-button back-button">Kembali ke Dashboard</a>
          </div>
        </div>
      </nav>

      <!-- Main Content -->
      <main class="category-main">
        <div class="category-header">
          <div class="category-icon-large">${categoryData.icon}</div>
          <h1 class="category-title">${categoryData.title}</h1>
          <p class="category-description">${categoryData.description}</p>
        </div>

        <div class="category-content">
          <div class="learning-modules-section">
            <h2 class="section-title">Modul Pembelajaran</h2>
            <div class="modules-grid">
              <!-- Placeholder for learning modules -->
              <div class="module-placeholder">
                <h3>Modul pembelajaran akan segera tersedia</h3>
                <p>Halaman ini sedang dalam pengembangan. Modul pembelajaran untuk kategori "${categoryData.title}" akan ditambahkan dalam update mendatang.</p>
              </div>
            </div>
          </div>

          <div class="practice-section">
            <h2 class="section-title">Latihan Praktik</h2>
            <div class="practice-grid">
              <!-- Placeholder for practice items -->
              <div class="practice-placeholder">
                <h3>5 item latihan praktik akan tersedia</h3>
                <p>Setiap kategori akan memiliki 5 item latihan praktik yang dapat Anda gunakan untuk mengasah kemampuan setelah mempelajari modul pembelajaran.</p>
              </div>
            </div>
          </div>
        </div>
      </main>
    `;

    // Append to app container
    if (appContainer) {
      appContainer.appendChild(this.container);
    } else {
      document.body.appendChild(this.container);
    }

    return this.container;
  }

  bindEvents(presenter) {
    // Bind back button
    const backButton = this.container.querySelector('.back-button');
    if (backButton) {
      backButton.addEventListener('click', (e) => {
        e.preventDefault();
        presenter.handleBackToDashboard();
      });
    }

    // Bind scroll event for navigation transition
    this.bindScrollEvent();
  }

  bindScrollEvent() {
    const nav = this.container.querySelector('.category-nav');
    const navContainer = this.container.querySelector('.nav-container');
    
    if (!nav || !navContainer) return;

    let isScrolled = false;

    const handleScroll = () => {
      const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
      const shouldBeScrolled = scrollTop > 50;

      if (shouldBeScrolled !== isScrolled) {
        isScrolled = shouldBeScrolled;
        
        if (isScrolled) {
          nav.classList.add('scrolled');
          navContainer.classList.add('scrolled');
        } else {
          nav.classList.remove('scrolled');
          navContainer.classList.remove('scrolled');
        }
      }
    };

    window.addEventListener('scroll', handleScroll);
    
    // Store the handler for cleanup
    this.scrollHandler = handleScroll;
  }

  destroy() {
    if (this.scrollHandler) {
      window.removeEventListener('scroll', this.scrollHandler);
      this.scrollHandler = null;
    }
    
    if (this.container && this.container.parentNode) {
      this.container.parentNode.removeChild(this.container);
    }
  }

  mount(parent) {
    if (parent && this.container) {
      parent.appendChild(this.container);
    }
  }
}

export default CategoryView;
