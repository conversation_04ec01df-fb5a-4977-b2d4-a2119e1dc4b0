class Router {
  constructor() {
    this.routes = {};
    this.currentRoute = null;
    this.previousRoute = null;

    // Listen for hash changes
    window.addEventListener('hashchange', () => this.handleRouteChange());
    window.addEventListener('load', () => this.handleRouteChange());
  }

  addRoute(path, handler) {
    this.routes[path] = handler;
  }

  handleRouteChange() {
    const hash = window.location.hash.slice(1) || '/';
    const matchedRoute = this.matchRoute(hash);

    if (matchedRoute) {
      // Update route tracking
      this.previousRoute = this.currentRoute;
      this.currentRoute = hash;

      // Check if View Transition API is supported
      const supportsViewTransitions = 'startViewTransition' in document;

      if (supportsViewTransitions) {
        console.log('🎬 Using View Transition API for route change');
        document.startViewTransition(() => {
          this.executeRoute(hash, matchedRoute.handler, matchedRoute.params);
        });
      } else {
        console.log('🔄 Using fallback for route change');
        this.executeRoute(hash, matchedRoute.handler, matchedRoute.params);
      }
    } else {
      window.location.hash = '#/';
    }
  }

  matchRoute(path) {
    // First try exact match
    if (this.routes[path]) {
      return { handler: this.routes[path], params: {} };
    }

    // Try pattern matching for dynamic routes
    for (const routePattern in this.routes) {
      const params = this.extractParams(routePattern, path);
      if (params !== null) {
        return { handler: this.routes[routePattern], params };
      }
    }

    return null;
  }

  extractParams(pattern, path) {
    // Convert pattern like '/category/:id' to regex
    const paramNames = [];
    const regexPattern = pattern.replace(/:([^/]+)/g, (_, paramName) => {
      paramNames.push(paramName);
      return '([^/]+)';
    });

    const regex = new RegExp(`^${regexPattern}$`);
    const match = path.match(regex);

    if (match) {
      const params = {};
      paramNames.forEach((name, index) => {
        params[name] = match[index + 1];
      });
      return params;
    }

    return null;
  }

  executeRoute(hash, route, params = {}) {
    // Execute route handler
    if (hash === '/result') {
      const storedResult = sessionStorage.getItem('accentResult');
      if (storedResult) {
        const resultData = JSON.parse(storedResult);
        sessionStorage.removeItem('accentResult');
        route(resultData);
      } else {
        route();
      }
    } else if (Object.keys(params).length > 0) {
      // Route with parameters
      const paramValues = Object.values(params);
      route(...paramValues);
    } else {
      route();
    }
  }

  navigate(path) {
    window.location.hash = `#${path}`;
  }

  getCurrentRoute() {
    return this.currentRoute;
  }

  getPreviousRoute() {
    return this.previousRoute;
  }
}

export default Router;
