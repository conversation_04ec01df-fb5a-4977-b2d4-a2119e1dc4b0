import CategoryView from '../views/CategoryView.js';

class CategoryPresenter {
  constructor(model, categoryId) {
    this.model = model;
    this.view = null;
    this.categoryId = categoryId;
  }

  init() {
    // Set dashboard mode for body
    document.body.classList.add('dashboard-mode');

    // Get category data
    const categoryData = this.model.getCategoryById(this.categoryId);
    
    if (!categoryData) {
      console.error('Category not found:', this.categoryId);
      // Redirect to dashboard if category not found
      window.location.hash = '#dashboard';
      return;
    }

    // Set current category in model
    this.model.setCurrentCategory(this.categoryId);

    // Create and render view
    this.view = new CategoryView();
    this.render(categoryData);
    this.bindEvents();
  }

  render(categoryData) {
    const viewElement = this.view.render(categoryData);
    this.view.mount(document.getElementById('app'));
  }

  bindEvents() {
    if (this.view) {
      this.view.bindEvents(this);
    }
  }

  handleBackToDashboard() {
    // Navigate back to dashboard
    window.location.hash = '#dashboard';
  }

  destroy() {
    if (this.view) {
      this.view.destroy();
      this.view = null;
    }
    
    // Remove dashboard mode class
    document.body.classList.remove('dashboard-mode');
  }

  // Method to handle module selection (for future implementation)
  handleModuleSelect(moduleId) {
    console.log('Selected module:', moduleId);
    // Future implementation: navigate to module page
  }

  // Method to handle practice item selection (for future implementation)
  handlePracticeSelect(practiceId) {
    console.log('Selected practice:', practiceId);
    // Future implementation: navigate to practice page
  }
}

export default CategoryPresenter;
