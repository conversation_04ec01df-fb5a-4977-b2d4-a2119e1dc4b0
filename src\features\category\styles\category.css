/* Category Page Styles */

/* Category Container */
.category-container {
  min-height: 100vh;
  background: #ffffff;
  color: #1e293b;
  font-family: 'Poppins', sans-serif;
  position: relative;
}

/* Navigation */
.category-nav {
  background-color: transparent;
  position: sticky;
  top: 0;
  z-index: 10;
  padding-top: 10px;
  transition: padding-top 0.3s ease-in-out;
}

.category-nav.scrolled {
  padding-top: 5px;
}

.category-nav .nav-container {
  max-width: 1380px;
  margin: 20px auto 8px auto;
  padding: 0.8rem 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: linear-gradient(135deg, #0079FF 0%, #004AAD 100%);
  box-shadow: 0 4px 20px rgba(0, 121, 255, 0.3);
  border: none;
  border-radius: 0.75rem;
  transition: all 0.3s ease-in-out;
}

.category-nav .nav-container.scrolled {
  margin: 10px auto 8px auto;
  max-width: 1000px;
  background: linear-gradient(135deg, #0079FF 0%, #004AAD 100%);
  box-shadow: 0 4px 20px rgba(0, 121, 255, 0.4);
  backdrop-filter: blur(15px);
  -webkit-backdrop-filter: blur(12px);
  border-radius: 1rem;
}

.category-nav .nav-logo {
  font-size: 1.5rem;
  font-weight: 300;
  text-shadow: none;
}

.category-nav .nav-logo-main {
  color: #ffffff;
}

.category-nav .nav-buttons {
  display: flex;
  gap: 1rem;
  align-items: center;
}

.category-nav .nav-button {
  background: rgba(255, 255, 255, 0.2);
  color: #ffffff;
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  text-decoration: none;
  font-size: 0.9rem;
  font-weight: 500;
  border: 1px solid rgba(255, 255, 255, 0.3);
  transition: all 0.2s ease;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.category-nav .nav-button:hover {
  background: rgba(255, 255, 255, 0.3);
  border-color: rgba(255, 255, 255, 0.5);
  transform: translateY(-1px);
}

/* Main Content */
.category-main {
  max-width: 1400px;
  margin: 0 auto;
  padding: 2rem 1rem;
}

/* Category Header */
.category-header {
  text-align: center;
  margin-bottom: 3rem;
  padding: 2rem 0;
}

.category-icon-large {
  font-size: 4rem;
  margin-bottom: 1rem;
  display: block;
}

.category-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: #1e293b;
  margin-bottom: 1rem;
  text-shadow: none;
}

.category-description {
  font-size: 1.125rem;
  color: #64748b;
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.6;
}

/* Content Sections */
.category-content {
  display: grid;
  gap: 3rem;
}

.section-title {
  font-size: 1.75rem;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 1.5rem;
  text-align: center;
}

/* Learning Modules Section */
.learning-modules-section {
  background: #f8fafc;
  border-radius: 1rem;
  padding: 2rem;
  border: 1px solid #e2e8f0;
}

.modules-grid {
  display: grid;
  gap: 1rem;
}

/* Practice Section */
.practice-section {
  background: #f1f5f9;
  border-radius: 1rem;
  padding: 2rem;
  border: 1px solid #e2e8f0;
}

.practice-grid {
  display: grid;
  gap: 1rem;
}

/* Placeholder Styles */
.module-placeholder,
.practice-placeholder {
  text-align: center;
  padding: 3rem 2rem;
  background: #ffffff;
  border-radius: 0.75rem;
  border: 2px dashed #cbd5e1;
  color: #64748b;
}

.module-placeholder h3,
.practice-placeholder h3 {
  font-size: 1.25rem;
  font-weight: 600;
  color: #475569;
  margin-bottom: 1rem;
}

.module-placeholder p,
.practice-placeholder p {
  font-size: 1rem;
  line-height: 1.6;
  margin: 0;
}

/* Responsive Design */
@media (max-width: 768px) {
  .category-nav .nav-container {
    max-width: 95%;
    padding: 0.75rem 1.5rem;
    margin: 0 auto 15px auto;
    justify-content: space-between;
  }

  .category-nav .nav-container.scrolled {
    max-width: 90%;
  }

  .category-nav .nav-logo {
    font-size: 1.3rem;
  }

  .category-nav .nav-button {
    padding: 0.4rem 0.8rem;
    font-size: 0.8rem;
  }

  .category-main {
    padding: 1.5rem 1rem;
  }

  .category-header {
    margin-bottom: 2rem;
    padding: 1rem 0;
  }

  .category-icon-large {
    font-size: 3rem;
  }

  .category-title {
    font-size: 2rem;
  }

  .category-description {
    font-size: 1rem;
  }

  .section-title {
    font-size: 1.5rem;
  }

  .learning-modules-section,
  .practice-section {
    padding: 1.5rem;
  }

  .module-placeholder,
  .practice-placeholder {
    padding: 2rem 1rem;
  }
}

@media (max-width: 480px) {
  .category-nav .nav-container {
    max-width: 98%;
    padding: 0.75rem 1rem;
    margin: 0 auto 10px auto;
  }

  .category-nav .nav-container.scrolled {
    max-width: 95%;
  }

  .category-nav .nav-logo {
    font-size: 1.1rem;
  }

  .category-nav .nav-button {
    padding: 0.3rem 0.6rem;
    font-size: 0.75rem;
  }

  .category-title {
    font-size: 1.75rem;
  }

  .category-icon-large {
    font-size: 2.5rem;
  }

  .learning-modules-section,
  .practice-section {
    padding: 1rem;
  }
}
