/**
 * VisitTracker - Utility for tracking user visits
 * Uses localStorage to persist visit status across browser sessions
 */
class VisitTracker {
  constructor() {
    this.storageKey = 'speaking_platform_visited';
    this.visitTimeKey = 'speaking_platform_first_visit';
  }

  /**
   * Check if user has visited the website before
   * @returns {boolean} true if user has visited before, false otherwise
   */
  hasVisited() {
    try {
      const visited = localStorage.getItem(this.storageKey);
      return visited === 'true';
    } catch (error) {
      console.warn('localStorage not available, treating as first visit:', error);
      return false;
    }
  }

  /**
   * Mark user as having visited the website
   * Also records the timestamp of first visit
   */
  markAsVisited() {
    try {
      localStorage.setItem(this.storageKey, 'true');
      
      // Only set first visit time if it doesn't exist
      if (!localStorage.getItem(this.visitTimeKey)) {
        localStorage.setItem(this.visitTimeKey, new Date().toISOString());
      }
      
      console.log('✅ User visit status saved to localStorage');
    } catch (error) {
      console.warn('Failed to save visit status to localStorage:', error);
    }
  }

  /**
   * Get the timestamp of first visit
   * @returns {Date|null} Date of first visit or null if not available
   */
  getFirstVisitTime() {
    try {
      const timestamp = localStorage.getItem(this.visitTimeKey);
      return timestamp ? new Date(timestamp) : null;
    } catch (error) {
      console.warn('Failed to get first visit time:', error);
      return null;
    }
  }

  /**
   * Clear visit tracking data (for testing/debugging)
   */
  clearVisitData() {
    try {
      localStorage.removeItem(this.storageKey);
      localStorage.removeItem(this.visitTimeKey);
      console.log('🗑️ Visit tracking data cleared');
    } catch (error) {
      console.warn('Failed to clear visit data:', error);
    }
  }

  /**
   * Get visit statistics
   * @returns {object} Object containing visit information
   */
  getVisitStats() {
    return {
      hasVisited: this.hasVisited(),
      firstVisit: this.getFirstVisitTime(),
      isReturningUser: this.hasVisited()
    };
  }

  /**
   * Check if user should skip intro flow
   * @returns {boolean} true if user should skip intro, false otherwise
   */
  shouldSkipIntro() {
    return this.hasVisited();
  }
}

// Create singleton instance
const visitTracker = new VisitTracker();

// Make available globally for debugging
if (typeof window !== 'undefined') {
  window.visitTracker = visitTracker;
  
  // Add global helper functions for debugging
  window.clearVisitData = () => {
    visitTracker.clearVisitData();
    console.log('🔄 Visit data cleared - next page load will show intro flow');
  };
  
  window.checkVisitStatus = () => {
    const stats = visitTracker.getVisitStats();
    console.log('📊 Visit Status:', stats);
    return stats;
  };
}

export default visitTracker;
