class CategoryModel {
  constructor() {
    this.categories = {
      'peta-vokal-amerika': {
        id: 'peta-vokal-amerika',
        title: 'Peta Vokal Amerika',
        description: 'Kuasai bunyi vokal seperti pada \'cat\' dan \'cut\'.',
        icon: '👄',
        modules: []
      },
      'irama-dan-penekanan': {
        id: 'irama-dan-penekanan',
        title: '<PERSON><PERSON> dan <PERSON>',
        description: 'Fokus pada ritme stress-timed untuk alur bicara yang lebih natural.',
        icon: '🎵',
        modules: []
      },
      'gugus-konsonan': {
        id: 'gugus-konsonan',
        title: 'Gugus Konson<PERSON>',
        description: 'Ucapkan kata seperti \'strengths\' dan \'world\'.',
        icon: '🔡',
        modules: []
      },
      'membaca-paragraf': {
        id: 'membaca-paragraf',
        title: 'Membaca Paragraf',
        description: 'Latih kelancaran dan intonasi dalam konteks.',
        icon: '📖',
        modules: []
      },
      'skenario-dunia-nyata': {
        id: 'skenario-dunia-nyata',
        title: 'Skenario Dunia Nyata',
        description: 'Simulasi percakapan sehari-hari.',
        icon: '💬',
        modules: []
      },
      'latihan-intensif': {
        id: 'latihan-intensif',
        title: 'Latihan Intensif',
        description: 'Tantang diri Anda dengan kalimat acak.',
        icon: '🔥',
        modules: []
      }
    };
    this.currentCategory = null;
  }

  getCategoryById(id) {
    return this.categories[id] || null;
  }

  getCategoryByTitle(title) {
    // Convert title to ID format
    const id = this.titleToId(title);
    return this.getCategoryById(id);
  }

  titleToId(title) {
    return title.toLowerCase()
      .replace(/\s+/g, '-')
      .replace(/[^\w\-]/g, '');
  }

  setCurrentCategory(categoryId) {
    this.currentCategory = categoryId;
  }

  getCurrentCategory() {
    if (!this.currentCategory) return null;
    return this.getCategoryById(this.currentCategory);
  }

  getAllCategories() {
    return Object.values(this.categories);
  }

  // Method to add learning modules (for future implementation)
  addModule(categoryId, module) {
    if (this.categories[categoryId]) {
      this.categories[categoryId].modules.push(module);
    }
  }

  // Method to get modules for a category
  getModules(categoryId) {
    const category = this.getCategoryById(categoryId);
    return category ? category.modules : [];
  }
}

export default CategoryModel;
